# AI驱动规约治理平台 - 开发约定规约

## 代码规范

### 通用编码规范

#### 命名约定
- **变量命名**：使用camelCase（JavaScript/TypeScript）或snake_case（Python）
- **常量命名**：使用UPPER_SNAKE_CASE
- **类名**：使用PascalCase
- **文件名**：使用kebab-case或snake_case
- **数据库表名**：使用snake_case，复数形式

#### 代码组织
- **文件结构**：按功能模块组织，避免过深的目录嵌套
- **导入顺序**：标准库 → 第三方库 → 本地模块
- **函数长度**：单个函数不超过50行
- **类长度**：单个类不超过300行
- **文件长度**：单个文件不超过500行

#### 注释规范
- **文档注释**：所有公共API必须有完整的文档注释
- **行内注释**：解释复杂逻辑，不解释显而易见的代码
- **TODO注释**：格式为`TODO(author): description`
- **注释语言**：统一使用中文，API文档使用英文

### 后端代码规范（Java/Spring Boot）

#### 项目结构
```
src/main/java/com/specplatform/
├── controller/          # 控制器层
├── service/            # 业务逻辑层
├── repository/         # 数据访问层
├── entity/             # 实体类
├── dto/                # 数据传输对象
├── config/             # 配置类
├── exception/          # 异常处理
├── util/               # 工具类
└── constant/           # 常量定义
```

#### 编码约定
- **Controller层**：只处理HTTP请求响应，不包含业务逻辑
- **Service层**：包含核心业务逻辑，事务管理
- **Repository层**：只处理数据访问，不包含业务逻辑
- **异常处理**：统一异常处理，自定义业务异常
- **日志记录**：使用SLF4J + Logback，合理设置日志级别

#### 代码示例
```java
@RestController
@RequestMapping("/api/v1/specifications")
@Validated
public class SpecificationController {
    
    private final SpecificationService specificationService;
    
    @PostMapping
    public ResponseEntity<SpecificationDTO> createSpecification(
            @Valid @RequestBody CreateSpecificationRequest request) {
        SpecificationDTO spec = specificationService.createSpecification(request);
        return ResponseEntity.ok(spec);
    }
}
```

### 前端代码规范（React/TypeScript）

#### 项目结构
```
src/
├── components/         # 通用组件
├── pages/             # 页面组件
├── hooks/             # 自定义Hook
├── services/          # API服务
├── store/             # 状态管理
├── utils/             # 工具函数
├── types/             # TypeScript类型定义
├── constants/         # 常量定义
└── styles/            # 样式文件
```

#### 编码约定
- **组件命名**：使用PascalCase，文件名与组件名一致
- **Hook命名**：以use开头，使用camelCase
- **Props接口**：以Props结尾，如`ButtonProps`
- **状态管理**：使用Redux Toolkit，避免直接修改state
- **样式管理**：使用CSS Modules或styled-components

#### 代码示例
```typescript
interface SpecificationListProps {
  projectId: string;
  onSpecificationSelect: (spec: Specification) => void;
}

export const SpecificationList: React.FC<SpecificationListProps> = ({
  projectId,
  onSpecificationSelect
}) => {
  const { data: specifications, isLoading } = useGetSpecificationsQuery(projectId);
  
  if (isLoading) {
    return <Spin size="large" />;
  }
  
  return (
    <List
      dataSource={specifications}
      renderItem={(spec) => (
        <List.Item onClick={() => onSpecificationSelect(spec)}>
          {spec.title}
        </List.Item>
      )}
    />
  );
};
```

## 数据库规范

### 表设计规范
- **表名**：使用snake_case，复数形式
- **主键**：统一使用UUID类型
- **时间字段**：created_at, updated_at, deleted_at
- **软删除**：使用deleted_at字段，不物理删除
- **索引命名**：idx_表名_字段名

### 查询规范
- **避免SELECT ***：明确指定需要的字段
- **使用参数化查询**：防止SQL注入
- **合理使用索引**：避免全表扫描
- **分页查询**：大数据量查询必须分页
- **事务管理**：合理使用事务，避免长事务

### 迁移规范
- **版本控制**：所有数据库变更通过迁移脚本
- **向后兼容**：新版本必须兼容旧版本
- **回滚脚本**：每个迁移都要有对应的回滚脚本
- **测试验证**：迁移前在测试环境验证

## API设计规范

### RESTful API设计
- **URL设计**：使用名词，避免动词
- **HTTP方法**：GET（查询）、POST（创建）、PUT（更新）、DELETE（删除）
- **状态码**：正确使用HTTP状态码
- **版本控制**：在URL中包含版本号，如/api/v1/

### 请求响应格式
```json
// 统一响应格式
{
  "code": 200,
  "message": "success",
  "data": {
    // 实际数据
  },
  "timestamp": "2025-01-29T10:00:00Z"
}

// 分页响应格式
{
  "code": 200,
  "message": "success",
  "data": {
    "items": [],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 100,
      "pages": 5
    }
  }
}
```

### 错误处理
- **错误码**：使用业务错误码，如SPEC_001
- **错误信息**：提供清晰的错误描述
- **国际化**：支持多语言错误信息
- **日志记录**：记录详细的错误日志

## 测试规范

### 测试策略
- **单元测试**：覆盖率 > 80%
- **集成测试**：测试服务间交互
- **端到端测试**：测试完整业务流程
- **性能测试**：关键接口性能测试
- **安全测试**：安全漏洞扫描

### 测试约定
- **测试文件命名**：*.test.js 或 *.spec.js
- **测试用例命名**：描述性命名，说明测试场景
- **测试数据**：使用工厂模式生成测试数据
- **Mock使用**：合理使用Mock，避免过度Mock

### 测试示例
```typescript
describe('SpecificationService', () => {
  let service: SpecificationService;
  let repository: MockType<SpecificationRepository>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        SpecificationService,
        {
          provide: SpecificationRepository,
          useFactory: repositoryMockFactory,
        },
      ],
    }).compile();

    service = module.get<SpecificationService>(SpecificationService);
    repository = module.get(SpecificationRepository);
  });

  describe('createSpecification', () => {
    it('should create a new specification', async () => {
      const createDto = { title: 'Test Spec', content: 'Test content' };
      const expectedSpec = { id: '1', ...createDto };
      
      repository.save.mockReturnValue(expectedSpec);
      
      const result = await service.createSpecification(createDto);
      
      expect(result).toEqual(expectedSpec);
      expect(repository.save).toHaveBeenCalledWith(createDto);
    });
  });
});
```

## 文档规范

### 文档类型
- **API文档**：使用OpenAPI/Swagger规范
- **架构文档**：使用C4模型或类似方法
- **用户文档**：面向最终用户的使用指南
- **开发文档**：面向开发者的技术文档

### 文档约定
- **格式**：统一使用Markdown格式
- **结构**：清晰的层次结构，使用标题分级
- **图表**：使用Mermaid.js绘制流程图和架构图
- **更新**：代码变更时同步更新文档

### 文档模板
```markdown
# 功能名称

## 概述
简要描述功能的目的和价值

## 用户故事
- 作为[角色]，我希望[功能]，以便[价值]

## 验收标准
- [ ] 标准1
- [ ] 标准2

## 技术设计
### 架构图
```mermaid
graph TD
    A[用户] --> B[API]
    B --> C[服务]
```

### API接口
```yaml
paths:
  /api/v1/resource:
    post:
      summary: 创建资源
```

## 实现任务
- [ ] 任务1
- [ ] 任务2
```

## 版本控制规范

### Git工作流
- **主分支**：main/master（生产环境）
- **开发分支**：develop（开发环境）
- **功能分支**：feature/功能名称
- **修复分支**：hotfix/问题描述
- **发布分支**：release/版本号

### 提交规范
```
<type>(<scope>): <subject>

<body>

<footer>
```

#### 提交类型
- **feat**：新功能
- **fix**：修复bug
- **docs**：文档更新
- **style**：代码格式调整
- **refactor**：代码重构
- **test**：测试相关
- **chore**：构建过程或辅助工具的变动

#### 提交示例
```
feat(spec): add specification generation API

- Add POST /api/v1/specifications endpoint
- Implement AI-powered spec generation
- Add input validation and error handling

Closes #123
```

## 代码审查规范

### 审查流程
1. **创建Pull Request**：包含清晰的描述和测试说明
2. **自动检查**：通过CI/CD的自动检查
3. **同行审查**：至少一个同事审查
4. **架构审查**：涉及架构变更需要架构师审查
5. **合并代码**：审查通过后合并到目标分支

### 审查要点
- **功能正确性**：代码是否实现了预期功能
- **代码质量**：是否遵循编码规范
- **性能考虑**：是否存在性能问题
- **安全性**：是否存在安全漏洞
- **可维护性**：代码是否易于理解和维护

### 审查工具
- **GitHub/GitLab**：代码审查平台
- **SonarQube**：代码质量检查
- **ESLint/Prettier**：代码格式检查
- **Security Scanner**：安全漏洞扫描

---

**文档版本**：v1.0  
**创建日期**：2025-01-29  
**负责人**：开发团队  
**审核人**：技术负责人、架构师
