# MCP规约驱动开发工具 - 开发约定规约

## 代码规范

### Python编码规范

#### 命名约定
- **变量命名**：使用snake_case
- **常量命名**：使用UPPER_SNAKE_CASE
- **类名**：使用PascalCase
- **函数名**：使用snake_case
- **模块名**：使用snake_case
- **包名**：使用小写字母，避免下划线

#### 代码组织
- **文件结构**：按功能模块组织，遵循Python包结构
- **导入顺序**：标准库 → 第三方库 → 本地模块
- **函数长度**：单个函数不超过50行
- **类长度**：单个类不超过200行
- **文件长度**：单个文件不超过300行

#### 注释规范
- **文档字符串**：所有公共函数和类必须有docstring
- **行内注释**：解释复杂逻辑，使用中文
- **TODO注释**：格式为`# TODO: description`
- **类型注解**：使用Python类型注解提高代码可读性

### 项目结构规范

#### 源码结构
```
src/
├── mcp_spec/
│   ├── __init__.py
│   ├── cli.py              # 命令行入口
│   ├── core/
│   │   ├── __init__.py
│   │   ├── generator.py    # 规约生成核心
│   │   ├── parser.py       # 需求解析
│   │   ├── tracker.py      # 任务跟踪
│   │   └── watcher.py      # 文档监控
│   ├── mcp/
│   │   ├── __init__.py
│   │   ├── server.py       # MCP服务器
│   │   └── handlers.py     # MCP处理器
│   ├── templates/          # 规约模板
│   │   ├── requirements.md.j2
│   │   ├── design.md.j2
│   │   └── tasks.md.j2
│   └── utils/
│       ├── __init__.py
│       ├── file_utils.py
│       └── config_utils.py
└── tests/
    ├── __init__.py
    ├── test_cli.py
    ├── test_generator.py
    └── fixtures/
```

#### 编码约定
- **模块导入**：使用绝对导入，避免相对导入
- **异常处理**：自定义异常类，提供清晰的错误信息
- **日志记录**：使用Python标准logging模块
- **配置管理**：使用Pydantic进行配置验证
- **类型注解**：所有公共函数必须有类型注解

#### 代码示例
```python
from typing import Dict, List, Optional
from pydantic import BaseModel
import click

class SpecificationConfig(BaseModel):
    """规约配置模型"""
    project_name: str
    tech_stack: List[str]
    ai_provider: str = "mcp"
    max_tokens: int = 4000

class SpecificationGenerator:
    """规约生成器"""

    def __init__(self, config: SpecificationConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)

    async def generate_requirements(
        self,
        intent: str,
        context: Optional[Dict] = None
    ) -> str:
        """生成需求规约

        Args:
            intent: 功能需求描述
            context: 项目上下文信息

        Returns:
            生成的需求规约文档

        Raises:
            GenerationError: 规约生成失败
        """
        try:
            # 实现规约生成逻辑
            pass
        except Exception as e:
            self.logger.error(f"规约生成失败: {e}")
            raise GenerationError(f"无法生成规约: {e}")
```

## 文件和配置规范

### 配置文件规范
- **格式**：使用JSON格式，支持注释的JSONC
- **命名**：使用kebab-case，如`project-config.json`
- **结构**：层次清晰，避免过深嵌套
- **验证**：使用Pydantic模型验证配置

### 文档文件规范
- **格式**：统一使用Markdown格式
- **命名**：使用kebab-case，如`user-authentication.md`
- **结构**：使用标准的文档模板
- **编码**：使用UTF-8编码

### 模板文件规范
- **格式**：使用Jinja2模板语法
- **命名**：以`.j2`结尾，如`requirements.md.j2`
- **变量**：使用snake_case命名模板变量
- **注释**：在模板中添加说明注释

## MCP协议规范

### MCP工具定义
```python
# MCP工具定义示例
TOOLS = [
    {
        "name": "generate_spec",
        "description": "生成功能规约文档",
        "inputSchema": {
            "type": "object",
            "properties": {
                "intent": {
                    "type": "string",
                    "description": "功能需求描述"
                },
                "mode": {
                    "type": "string",
                    "enum": ["quick", "full"],
                    "description": "生成模式"
                }
            },
            "required": ["intent"]
        }
    }
]
```

### MCP消息处理
- **请求验证**：验证输入参数的完整性和有效性
- **错误处理**：返回标准的MCP错误响应
- **日志记录**：记录所有MCP调用和响应
- **超时处理**：设置合理的超时时间

### MCP服务器配置
```json
{
  "name": "spec-generator",
  "version": "1.0.0",
  "tools": [
    "generate_spec",
    "list_tasks",
    "update_task_status",
    "sync_documents"
  ],
  "capabilities": {
    "tools": true,
    "resources": false,
    "prompts": false
  }
}
```

## 测试规范

### 测试策略
- **单元测试**：覆盖率 > 80%，使用pytest框架
- **集成测试**：测试MCP服务器和CLI工具的集成
- **功能测试**：测试完整的规约生成流程
- **性能测试**：测试规约生成的响应时间
- **兼容性测试**：测试与不同AI工具的兼容性

### 测试约定
- **测试文件命名**：`test_*.py`
- **测试类命名**：`Test*`，如`TestSpecificationGenerator`
- **测试方法命名**：`test_*`，描述性命名
- **测试数据**：使用fixtures管理测试数据
- **Mock使用**：使用unittest.mock进行外部依赖Mock

### 测试示例
```python
import pytest
from unittest.mock import Mock, patch
from mcp_spec.core.generator import SpecificationGenerator
from mcp_spec.core.config import SpecificationConfig

class TestSpecificationGenerator:
    """规约生成器测试类"""

    @pytest.fixture
    def config(self):
        """测试配置fixture"""
        return SpecificationConfig(
            project_name="test-project",
            tech_stack=["Python", "FastAPI"],
            ai_provider="mcp"
        )

    @pytest.fixture
    def generator(self, config):
        """生成器实例fixture"""
        return SpecificationGenerator(config)

    @patch('mcp_spec.core.generator.ai_client')
    async def test_generate_requirements_success(self, mock_ai_client, generator):
        """测试成功生成需求规约"""
        # Arrange
        intent = "添加用户登录功能"
        expected_spec = "# 用户登录功能需求规约\n..."
        mock_ai_client.generate.return_value = expected_spec

        # Act
        result = await generator.generate_requirements(intent)

        # Assert
        assert result == expected_spec
        mock_ai_client.generate.assert_called_once()

    async def test_generate_requirements_with_invalid_intent(self, generator):
        """测试无效输入的错误处理"""
        # Arrange
        intent = ""

        # Act & Assert
        with pytest.raises(ValueError, match="需求描述不能为空"):
            await generator.generate_requirements(intent)
```

## 文档规范

### 文档类型
- **规约文档**：需求规约、设计规约、任务规约
- **用户文档**：CLI工具使用指南、MCP集成指南
- **开发文档**：架构设计、API文档
- **项目文档**：项目宪法、开发约定

### 文档约定
- **格式**：统一使用Markdown格式
- **结构**：清晰的层次结构，使用标题分级
- **图表**：使用Mermaid.js绘制流程图和架构图
- **更新**：代码变更时同步更新文档
- **版本控制**：文档纳入Git版本控制

### 规约文档模板
```markdown
# 功能名称

## 需求概述
简要描述功能的目的和价值

## 用户故事
- 作为[角色]，我希望[功能]，以便[价值]

## 验收标准
- [ ] 标准1：具体的可验证条件
- [ ] 标准2：具体的可验证条件

## 技术设计
### 架构图
```mermaid
graph TD
    A[用户输入] --> B[CLI工具]
    B --> C[MCP服务器]
    C --> D[规约生成]
```

### 实现方案
- 技术选型说明
- 关键算法描述
- 数据结构设计

## 实现任务
- [ ] 任务1：具体的开发任务
- [ ] 任务2：具体的开发任务

## 测试计划
- [ ] 单元测试
- [ ] 集成测试
- [ ] 用户验收测试
```

## 版本控制规范

### Git工作流
- **主分支**：main（稳定版本）
- **开发分支**：develop（开发版本）
- **功能分支**：feature/功能名称
- **修复分支**：hotfix/问题描述
- **发布分支**：release/版本号

### 提交规范
```
<type>(<scope>): <subject>

<body>

<footer>
```

#### 提交类型
- **feat**：新功能
- **fix**：修复bug
- **docs**：文档更新
- **style**：代码格式调整
- **refactor**：代码重构
- **test**：测试相关
- **chore**：构建过程或辅助工具的变动

#### 提交示例
```
feat(mcp): add specification generation tool

- Add generate_spec MCP tool
- Implement requirements parsing
- Add template system for spec generation

Closes #123
```

## 发布和部署规范

### 版本号规范
- 使用语义化版本号：`MAJOR.MINOR.PATCH`
- **MAJOR**：不兼容的API变更
- **MINOR**：向后兼容的功能新增
- **PATCH**：向后兼容的问题修复

### 发布流程
1. **代码冻结**：停止新功能开发
2. **测试验证**：完整的测试验证
3. **文档更新**：更新用户文档和变更日志
4. **版本标记**：创建Git标签
5. **包发布**：发布到PyPI

### 部署检查清单
- [ ] 所有测试通过
- [ ] 文档已更新
- [ ] 版本号已更新
- [ ] 变更日志已更新
- [ ] MCP工具定义已验证
- [ ] 与主流AI工具的兼容性已测试

---

**文档版本**：v2.0
**创建日期**：2025-01-29
**更新日期**：2025-01-29
**负责人**：个人开发者
**项目类型**：Python CLI工具 + MCP服务器
