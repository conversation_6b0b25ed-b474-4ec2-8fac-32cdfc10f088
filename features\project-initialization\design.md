# 项目初始化功能 - 设计规约

## 设计概述

### 设计目标
设计一个简单、高效、可扩展的项目初始化系统，能够快速生成标准化的项目规约框架，支持多种技术栈模板和个性化配置，同时提供MCP协议集成能力。

### 设计原则
- **简单优先**: 最小化用户操作步骤，提供直观的交互体验
- **模板驱动**: 基于模板系统实现灵活配置和扩展
- **可扩展性**: 支持自定义模板和配置，便于功能扩展
- **错误友好**: 提供清晰的错误信息和恢复建议
- **协议兼容**: 完全兼容MCP协议规范

## 架构设计

### 整体架构
```mermaid
graph TD
    A[CLI命令] --> B[初始化控制器]
    C[MCP工具调用] --> B
    
    B --> D[配置收集器]
    B --> E[模板引擎]
    B --> F[文件生成器]
    B --> G[验证器]
    
    D --> H[交互式界面]
    D --> I[配置验证器]
    D --> J[默认配置加载器]
    
    E --> K[模板加载器]
    E --> L[变量替换器]
    E --> M[模板缓存]
    
    F --> N[目录创建器]
    F --> O[文件写入器]
    F --> P[权限管理器]
    
    G --> Q[结构验证器]
    G --> R[内容验证器]
    
    K --> S[内置模板库]
    K --> T[自定义模板]
```

### 核心组件设计

#### 1. 初始化控制器 (InitializationController)
```python
class InitializationController:
    """项目初始化主控制器"""
    
    def __init__(self):
        self.config_collector = ConfigurationCollector()
        self.template_engine = TemplateEngine()
        self.file_generator = FileGenerator()
        self.validator = ProjectValidator()
        self.logger = logging.getLogger(__name__)
    
    async def initialize_project(
        self, 
        name: str, 
        template: str = "basic",
        config: Optional[Dict] = None,
        quiet: bool = False,
        force: bool = False,
        output_dir: str = "."
    ) -> InitializationResult:
        """执行项目初始化流程"""
        
    def validate_project_name(self, name: str) -> bool:
        """验证项目名称有效性"""
        
    def check_directory_exists(self, path: Path) -> bool:
        """检查目录是否已存在"""
```

#### 2. 配置收集器 (ConfigurationCollector)
```python
class ConfigurationCollector:
    """项目配置信息收集器"""
    
    def collect_interactive_config(self) -> ProjectConfig:
        """交互式收集配置信息"""
        
    def collect_from_args(self, args: Dict) -> ProjectConfig:
        """从命令行参数收集配置"""
        
    def merge_configurations(self, *configs: ProjectConfig) -> ProjectConfig:
        """合并多个配置对象"""
        
    def validate_config(self, config: ProjectConfig) -> ValidationResult:
        """验证配置完整性和有效性"""
```

#### 3. 模板引擎 (TemplateEngine)
```python
class TemplateEngine:
    """基于Jinja2的模板处理引擎"""
    
    def __init__(self, template_dirs: List[str]):
        self.template_dirs = template_dirs
        self.env = self._setup_jinja_env()
        self.cache = TemplateCache()
    
    def load_template_set(self, template_name: str) -> TemplateSet:
        """加载模板集合"""
        
    def render_template(self, template: str, variables: Dict) -> str:
        """渲染单个模板"""
        
    def render_template_set(self, template_set: TemplateSet, 
                           config: ProjectConfig) -> Dict[str, str]:
        """渲染整个模板集合"""
        
    def list_available_templates(self) -> List[TemplateInfo]:
        """列出可用的模板"""
```

#### 4. 文件生成器 (FileGenerator)
```python
class FileGenerator:
    """文件和目录生成器"""
    
    def create_project_structure(
        self, 
        base_path: Path, 
        structure: ProjectStructure
    ) -> GenerationResult:
        """创建完整的项目结构"""
        
    def create_directory_structure(self, directories: List[str]) -> bool:
        """创建目录结构"""
        
    def write_file(self, path: Path, content: str, 
                   encoding: str = "utf-8") -> bool:
        """写入文件内容"""
        
    def copy_static_files(self, source_dir: Path, 
                         dest_dir: Path) -> bool:
        """复制静态文件"""
```

## 数据结构设计

### 核心数据模型
```python
from pydantic import BaseModel, Field, validator
from typing import List, Dict, Optional, Any
from datetime import datetime
from enum import Enum

class TechStack(str, Enum):
    """技术栈枚举"""
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    REACT = "react"
    VUE = "vue"
    NODEJS = "nodejs"
    FASTAPI = "fastapi"
    DJANGO = "django"

class TeamExperience(str, Enum):
    """团队经验水平"""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"

class ProjectConfig(BaseModel):
    """项目配置数据模型"""
    # 基本信息
    name: str = Field(..., min_length=1, max_length=100)
    description: str = Field(..., min_length=1, max_length=500)
    version: str = Field(default="1.0.0")
    
    # 技术栈
    tech_stack: List[TechStack] = Field(default_factory=list)
    template: str = Field(default="basic")
    
    # 团队信息
    team_size: int = Field(default=1, ge=1, le=100)
    team_experience: TeamExperience = Field(default=TeamExperience.INTERMEDIATE)
    
    # 项目设置
    performance_requirements: str = Field(default="")
    security_requirements: str = Field(default="")
    design_principles: List[str] = Field(default_factory=list)
    constraints: List[str] = Field(default_factory=list)
    
    # 工具配置
    mcp_integration: bool = Field(default=True)
    ai_provider: str = Field(default="mcp")
    language: str = Field(default="zh-CN")
    
    # 元数据
    created_at: datetime = Field(default_factory=datetime.now)
    created_by: str = Field(default="mcp-spec-tool")
    
    @validator('name')
    def validate_name(cls, v):
        """验证项目名称格式"""
        import re
        if not re.match(r'^[a-zA-Z][a-zA-Z0-9_-]*$', v):
            raise ValueError('项目名称必须以字母开头，只能包含字母、数字、下划线和连字符')
        return v
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class TemplateInfo(BaseModel):
    """模板信息模型"""
    name: str
    display_name: str
    description: str
    tech_stack: List[TechStack]
    files: List[str]
    variables: Dict[str, Any]
    dependencies: List[str] = Field(default_factory=list)
    version: str = Field(default="1.0.0")

class ProjectStructure(BaseModel):
    """项目结构定义"""
    directories: List[str]
    files: Dict[str, str]  # path -> template_name
    static_files: Dict[str, str] = Field(default_factory=dict)  # source -> dest
    
    def get_all_paths(self) -> List[str]:
        """获取所有路径"""
        return self.directories + list(self.files.keys()) + list(self.static_files.values())

class InitializationResult(BaseModel):
    """初始化结果模型"""
    success: bool
    project_path: Optional[str] = None
    created_files: List[str] = Field(default_factory=list)
    created_directories: List[str] = Field(default_factory=list)
    error: Optional[str] = None
    warnings: List[str] = Field(default_factory=list)
    execution_time: float = 0.0
```

### 模板系统数据结构
```python
@dataclass
class TemplateSet:
    """模板集合"""
    name: str
    info: TemplateInfo
    templates: Dict[str, str]  # filename -> template_content
    structure: ProjectStructure
    
    def get_template_variables(self) -> Set[str]:
        """获取模板中使用的所有变量"""
        variables = set()
        for template_content in self.templates.values():
            # 解析Jinja2模板中的变量
            variables.update(self._extract_variables(template_content))
        return variables

# 预定义的项目结构模板
STANDARD_STRUCTURE = ProjectStructure(
    directories=[
        ".spec",
        "features", 
        "src",
        "tests",
        "docs",
        "templates"
    ],
    files={
        ".spec/project.md": "project.md.j2",
        ".spec/architecture.md": "architecture.md.j2",
        ".spec/conventions.md": "conventions.md.j2", 
        ".spec/config.json": "config.json.j2",
        ".spec/README.md": "spec_readme.md.j2",
        "features/README.md": "features_readme.md.j2",
        "README.md": "project_readme.md.j2",
        ".gitignore": "gitignore.j2"
    }
)

PYTHON_STRUCTURE = ProjectStructure(
    directories=[
        ".spec",
        "features",
        "src/mcp_spec",
        "src/mcp_spec/core",
        "src/mcp_spec/mcp", 
        "src/mcp_spec/templates",
        "src/mcp_spec/utils",
        "tests",
        "docs"
    ],
    files={
        **STANDARD_STRUCTURE.files,
        "src/mcp_spec/__init__.py": "python_init.py.j2",
        "src/mcp_spec/cli.py": "python_cli.py.j2",
        "setup.py": "python_setup.py.j2",
        "requirements.txt": "python_requirements.txt.j2",
        "pyproject.toml": "python_pyproject.toml.j2"
    }
)
```

## 接口设计

### CLI接口设计
```python
import click
from typing import Optional, List

@click.command()
@click.argument('project_name')
@click.option('--template', '-t', default='basic', 
              help='项目模板名称 (basic, python, react, vue, nodejs)')
@click.option('--tech-stack', '-s', multiple=True,
              help='技术栈选择，可多选')
@click.option('--quiet', '-q', is_flag=True, 
              help='静默模式，使用默认配置')
@click.option('--force', '-f', is_flag=True, 
              help='强制覆盖已存在的目录')
@click.option('--output', '-o', default='.', 
              help='输出目录路径')
@click.option('--config', '-c', type=click.Path(exists=True),
              help='从配置文件加载设置')
@click.option('--dry-run', is_flag=True,
              help='预览模式，不实际创建文件')
def init(project_name: str, template: str, tech_stack: List[str],
         quiet: bool, force: bool, output: str, config: Optional[str],
         dry_run: bool):
    """初始化新项目的规约框架
    
    Examples:
        mcp-spec init my-project
        mcp-spec init my-project --template python --tech-stack fastapi
        mcp-spec init my-project --quiet --force
    """
    try:
        controller = InitializationController()
        
        # 构建配置
        init_config = {
            'name': project_name,
            'template': template,
            'tech_stack': list(tech_stack),
            'quiet': quiet,
            'force': force,
            'output_dir': output,
            'config_file': config,
            'dry_run': dry_run
        }
        
        # 执行初始化
        with click.progressbar(length=100, label='初始化项目') as bar:
            result = controller.initialize_project_with_progress(
                init_config, 
                progress_callback=bar.update
            )
        
        # 显示结果
        if result.success:
            click.echo(f"✅ 项目 '{project_name}' 初始化成功!")
            click.echo(f"📁 项目路径: {result.project_path}")
            click.echo(f"📄 创建文件: {len(result.created_files)} 个")
            click.echo(f"📂 创建目录: {len(result.created_directories)} 个")
            click.echo(f"⏱️  耗时: {result.execution_time:.2f} 秒")
            
            if result.warnings:
                click.echo("⚠️  警告信息:")
                for warning in result.warnings:
                    click.echo(f"   - {warning}")
        else:
            click.echo(f"❌ 初始化失败: {result.error}")
            raise click.Abort()
            
    except Exception as e:
        click.echo(f"❌ 发生错误: {e}")
        if click.get_current_context().obj.get('debug'):
            raise
        else:
            raise click.Abort()

@click.command()
def list_templates():
    """列出可用的项目模板"""
    controller = InitializationController()
    templates = controller.list_available_templates()
    
    click.echo("📋 可用的项目模板:")
    for template in templates:
        click.echo(f"  {template.name:<12} - {template.description}")
        click.echo(f"               技术栈: {', '.join(template.tech_stack)}")
        click.echo()
```

### MCP工具接口设计
```python
from mcp import Server, Tool
from mcp.types import TextContent, JSONSchema

# MCP工具定义
INITIALIZE_PROJECT_TOOL = Tool(
    name="initialize_project",
    description="初始化新项目的规约框架，生成标准化的项目结构和文档模板",
    inputSchema=JSONSchema(
        type="object",
        properties={
            "project_name": {
                "type": "string",
                "description": "项目名称，必须以字母开头，只能包含字母、数字、下划线和连字符",
                "pattern": "^[a-zA-Z][a-zA-Z0-9_-]*$",
                "minLength": 1,
                "maxLength": 100
            },
            "template": {
                "type": "string", 
                "description": "项目模板类型",
                "enum": ["basic", "python", "react", "vue", "nodejs"],
                "default": "basic"
            },
            "tech_stack": {
                "type": "array",
                "description": "技术栈列表",
                "items": {
                    "type": "string",
                    "enum": ["python", "javascript", "typescript", "react", "vue", "nodejs", "fastapi", "django"]
                }
            },
            "description": {
                "type": "string",
                "description": "项目描述",
                "maxLength": 500
            },
            "team_size": {
                "type": "integer",
                "description": "团队规模",
                "minimum": 1,
                "maximum": 100,
                "default": 1
            },
            "team_experience": {
                "type": "string",
                "description": "团队经验水平",
                "enum": ["beginner", "intermediate", "advanced", "expert"],
                "default": "intermediate"
            },
            "output_dir": {
                "type": "string",
                "description": "输出目录路径",
                "default": "."
            },
            "force": {
                "type": "boolean",
                "description": "是否强制覆盖已存在的目录",
                "default": false
            }
        },
        required=["project_name"]
    )
)

class MCPInitializationHandler:
    """MCP协议初始化处理器"""
    
    def __init__(self):
        self.controller = InitializationController()
    
    async def handle_initialize_project(self, arguments: Dict) -> TextContent:
        """处理项目初始化MCP工具调用"""
        try:
            # 参数验证
            project_config = ProjectConfig(**arguments)
            
            # 执行初始化
            result = await self.controller.initialize_project(
                name=project_config.name,
                template=project_config.template,
                config=project_config.dict(),
                force=arguments.get('force', False),
                output_dir=arguments.get('output_dir', '.')
            )
            
            # 构建响应
            if result.success:
                response = {
                    "status": "success",
                    "message": f"项目 '{project_config.name}' 初始化成功",
                    "project_path": result.project_path,
                    "created_files": len(result.created_files),
                    "created_directories": len(result.created_directories),
                    "execution_time": result.execution_time,
                    "warnings": result.warnings
                }
            else:
                response = {
                    "status": "error", 
                    "message": f"项目初始化失败: {result.error}",
                    "error_details": result.error
                }
            
            return TextContent(
                type="text",
                text=json.dumps(response, ensure_ascii=False, indent=2)
            )
            
        except ValidationError as e:
            return TextContent(
                type="text",
                text=json.dumps({
                    "status": "error",
                    "message": "参数验证失败",
                    "validation_errors": e.errors()
                }, ensure_ascii=False, indent=2)
            )
        except Exception as e:
            return TextContent(
                type="text", 
                text=json.dumps({
                    "status": "error",
                    "message": f"内部错误: {str(e)}"
                }, ensure_ascii=False, indent=2)
            )
```

## 实现细节

### 模板系统实现
```python
import os
from pathlib import Path
from jinja2 import Environment, FileSystemLoader, select_autoescape
from typing import Dict, List, Set
import re

class TemplateEngine:
    """高级模板引擎实现"""
    
    def __init__(self, template_dirs: List[str]):
        self.template_dirs = [Path(d) for d in template_dirs]
        self.env = self._setup_jinja_env()
        self.cache = {}
        self._load_template_registry()
    
    def _setup_jinja_env(self) -> Environment:
        """设置Jinja2环境"""
        env = Environment(
            loader=FileSystemLoader([str(d) for d in self.template_dirs]),
            autoescape=select_autoescape(['html', 'xml']),
            trim_blocks=True,
            lstrip_blocks=True,
            keep_trailing_newline=True
        )
        
        # 添加自定义过滤器
        env.filters['snake_case'] = self._snake_case_filter
        env.filters['kebab_case'] = self._kebab_case_filter
        env.filters['pascal_case'] = self._pascal_case_filter
        env.filters['current_year'] = lambda x: datetime.now().year
        
        return env
    
    def _load_template_registry(self):
        """加载模板注册表"""
        self.template_registry = {}
        for template_dir in self.template_dirs:
            registry_file = template_dir / "templates.json"
            if registry_file.exists():
                with open(registry_file, 'r', encoding='utf-8') as f:
                    registry = json.load(f)
                    self.template_registry.update(registry)
    
    def load_template_set(self, template_name: str) -> TemplateSet:
        """加载完整的模板集合"""
        if template_name in self.cache:
            return self.cache[template_name]
        
        template_info = self.template_registry.get(template_name)
        if not template_info:
            raise TemplateNotFoundError(f"模板 '{template_name}' 不存在")
        
        # 加载模板文件
        templates = {}
        for file_path in template_info['files']:
            try:
                template_content = self.env.get_template(file_path).source
                templates[file_path] = template_content
            except Exception as e:
                raise TemplateLoadError(f"加载模板文件 '{file_path}' 失败: {e}")
        
        # 构建模板集合
        template_set = TemplateSet(
            name=template_name,
            info=TemplateInfo(**template_info),
            templates=templates,
            structure=self._build_structure_from_info(template_info)
        )
        
        # 缓存模板集合
        self.cache[template_name] = template_set
        return template_set
    
    def render_template_set(self, template_set: TemplateSet, 
                           config: ProjectConfig) -> Dict[str, str]:
        """渲染整个模板集合"""
        rendered_files = {}
        template_vars = self._build_template_variables(config)
        
        for file_path, template_name in template_set.structure.files.items():
            try:
                template = self.env.get_template(template_name)
                rendered_content = template.render(**template_vars)
                rendered_files[file_path] = rendered_content
            except Exception as e:
                raise TemplateRenderError(f"渲染模板 '{template_name}' 失败: {e}")
        
        return rendered_files
    
    def _build_template_variables(self, config: ProjectConfig) -> Dict:
        """构建模板变量字典"""
        return {
            'project': config.dict(),
            'current_date': datetime.now().strftime('%Y-%m-%d'),
            'current_year': datetime.now().year,
            'tool_version': '1.0.0',
            'generated_by': 'mcp-spec-tool'
        }
    
    @staticmethod
    def _snake_case_filter(text: str) -> str:
        """转换为snake_case"""
        return re.sub(r'(?<!^)(?=[A-Z])', '_', text).lower()
    
    @staticmethod
    def _kebab_case_filter(text: str) -> str:
        """转换为kebab-case"""
        return re.sub(r'(?<!^)(?=[A-Z])', '-', text).lower()
    
    @staticmethod
    def _pascal_case_filter(text: str) -> str:
        """转换为PascalCase"""
        return ''.join(word.capitalize() for word in re.split(r'[-_\s]', text))
```

### 交互式配置实现
```python
import click
from typing import List, Dict, Optional
from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.table import Table

class InteractiveConfigCollector:
    """增强的交互式配置收集器"""
    
    def __init__(self):
        self.console = Console()
    
    def collect_project_info(self) -> ProjectConfig:
        """收集完整的项目配置信息"""
        self.console.print("🚀 [bold blue]MCP规约驱动开发工具 - 项目初始化[/bold blue]")
        self.console.print()
        
        # 基本信息
        basic_info = self._collect_basic_info()
        
        # 技术栈选择
        tech_stack = self._collect_tech_stack()
        
        # 团队信息
        team_info = self._collect_team_info()
        
        # 项目设置
        project_settings = self._collect_project_settings()
        
        # 构建配置对象
        config_data = {
            **basic_info,
            'tech_stack': tech_stack,
            **team_info,
            **project_settings
        }
        
        return ProjectConfig(**config_data)
    
    def _collect_basic_info(self) -> Dict:
        """收集项目基本信息"""
        self.console.print("📋 [bold]项目基本信息[/bold]")
        
        name = Prompt.ask(
            "项目名称",
            console=self.console,
            default="my-project"
        )
        
        description = Prompt.ask(
            "项目描述", 
            console=self.console,
            default="一个基于MCP的规约驱动开发项目"
        )
        
        version = Prompt.ask(
            "初始版本",
            console=self.console, 
            default="1.0.0"
        )
        
        return {
            'name': name,
            'description': description,
            'version': version
        }
    
    def _collect_tech_stack(self) -> List[str]:
        """收集技术栈信息"""
        self.console.print("\n🛠️  [bold]技术栈选择[/bold]")
        
        available_stacks = [
            ("python", "Python - 通用编程语言"),
            ("javascript", "JavaScript - 前端开发语言"),
            ("typescript", "TypeScript - 类型安全的JavaScript"),
            ("react", "React - 前端UI框架"),
            ("vue", "Vue.js - 渐进式前端框架"),
            ("nodejs", "Node.js - JavaScript运行时"),
            ("fastapi", "FastAPI - 现代Python Web框架"),
            ("django", "Django - Python Web框架")
        ]
        
        # 显示选项表格
        table = Table(title="可选技术栈")
        table.add_column("序号", style="cyan", no_wrap=True)
        table.add_column("技术栈", style="magenta")
        table.add_column("描述", style="green")
        
        for i, (stack, desc) in enumerate(available_stacks, 1):
            table.add_row(str(i), stack, desc)
        
        self.console.print(table)
        
        choices = Prompt.ask(
            "\n请选择技术栈 (输入序号，多选用逗号分隔，如: 1,3,5)",
            console=self.console,
            default="1"
        )
        
        try:
            indices = [int(x.strip()) - 1 for x in choices.split(',')]
            selected_stacks = [available_stacks[i][0] for i in indices 
                             if 0 <= i < len(available_stacks)]
            return selected_stacks
        except (ValueError, IndexError):
            self.console.print("❌ 输入格式错误，使用默认选择: Python")
            return ["python"]
    
    def _collect_team_info(self) -> Dict:
        """收集团队信息"""
        self.console.print("\n👥 [bold]团队信息[/bold]")
        
        team_size = Prompt.ask(
            "团队规模",
            console=self.console,
            default="1"
        )
        
        experience_options = ["beginner", "intermediate", "advanced", "expert"]
        experience = Prompt.ask(
            "团队经验水平",
            console=self.console,
            choices=experience_options,
            default="intermediate"
        )
        
        return {
            'team_size': int(team_size),
            'team_experience': experience
        }
    
    def _collect_project_settings(self) -> Dict:
        """收集项目设置"""
        self.console.print("\n⚙️  [bold]项目设置[/bold]")
        
        # MCP集成
        mcp_integration = Confirm.ask(
            "启用MCP协议集成",
            console=self.console,
            default=True
        )
        
        # AI提供商
        ai_provider = "mcp" if mcp_integration else "none"
        
        # 语言设置
        language = Prompt.ask(
            "界面语言",
            console=self.console,
            choices=["zh-CN", "en-US"],
            default="zh-CN"
        )
        
        # 性能需求
        performance_req = Prompt.ask(
            "性能需求 (可选)",
            console=self.console,
            default="标准性能要求"
        )
        
        # 安全需求
        security_req = Prompt.ask(
            "安全需求 (可选)",
            console=self.console,
            default="基础安全要求"
        )
        
        return {
            'mcp_integration': mcp_integration,
            'ai_provider': ai_provider,
            'language': language,
            'performance_requirements': performance_req,
            'security_requirements': security_req
        }
```

## 错误处理设计

### 异常类层次结构
```python
class InitializationError(Exception):
    """初始化相关错误基类"""
    def __init__(self, message: str, details: Optional[Dict] = None):
        super().__init__(message)
        self.message = message
        self.details = details or {}

class ProjectExistsError(InitializationError):
    """项目已存在错误"""
    pass

class TemplateNotFoundError(InitializationError):
    """模板未找到错误"""
    pass

class TemplateLoadError(InitializationError):
    """模板加载错误"""
    pass

class TemplateRenderError(InitializationError):
    """模板渲染错误"""
    pass

class ConfigurationError(InitializationError):
    """配置错误"""
    pass

class FileGenerationError(InitializationError):
    """文件生成错误"""
    pass

class ValidationError(InitializationError):
    """验证错误"""
    pass
```

### 错误处理策略
```python
class ErrorHandler:
    """统一错误处理器"""

    @staticmethod
    def handle_initialization_error(error: Exception) -> Dict[str, Any]:
        """处理初始化错误"""
        error_map = {
            ProjectExistsError: {
                'code': 'PROJECT_EXISTS',
                'message': '项目目录已存在',
                'suggestion': '请使用 --force 选项强制覆盖，或选择其他目录'
            },
            TemplateNotFoundError: {
                'code': 'TEMPLATE_NOT_FOUND',
                'message': '指定的模板不存在',
                'suggestion': '请使用 mcp-spec list-templates 查看可用模板'
            },
            ConfigurationError: {
                'code': 'CONFIGURATION_ERROR',
                'message': '配置参数错误',
                'suggestion': '请检查输入参数的格式和有效性'
            },
            FileGenerationError: {
                'code': 'FILE_GENERATION_ERROR',
                'message': '文件生成失败',
                'suggestion': '请检查目录权限和磁盘空间'
            }
        }

        error_type = type(error)
        error_info = error_map.get(error_type, {
            'code': 'UNKNOWN_ERROR',
            'message': '未知错误',
            'suggestion': '请联系开发者或查看详细日志'
        })

        return {
            'error_code': error_info['code'],
            'error_message': error_info['message'],
            'suggestion': error_info['suggestion'],
            'details': str(error),
            'error_type': error_type.__name__
        }
```

## 性能优化设计

### 缓存策略
```python
from functools import lru_cache
from typing import Dict, Any
import hashlib

class TemplateCache:
    """模板缓存管理器"""

    def __init__(self, max_size: int = 100):
        self.max_size = max_size
        self.cache: Dict[str, Any] = {}
        self.access_count: Dict[str, int] = {}

    def get(self, key: str) -> Optional[Any]:
        """获取缓存项"""
        if key in self.cache:
            self.access_count[key] = self.access_count.get(key, 0) + 1
            return self.cache[key]
        return None

    def set(self, key: str, value: Any) -> None:
        """设置缓存项"""
        if len(self.cache) >= self.max_size:
            self._evict_least_used()

        self.cache[key] = value
        self.access_count[key] = 1

    def _evict_least_used(self) -> None:
        """淘汰最少使用的缓存项"""
        if not self.cache:
            return

        least_used_key = min(self.access_count.keys(),
                           key=lambda k: self.access_count[k])
        del self.cache[least_used_key]
        del self.access_count[least_used_key]

class FileGenerator:
    """优化的文件生成器"""

    def __init__(self):
        self.file_cache = {}

    async def create_project_structure_async(
        self,
        base_path: Path,
        structure: ProjectStructure,
        rendered_files: Dict[str, str]
    ) -> GenerationResult:
        """异步创建项目结构"""
        import asyncio

        # 创建目录
        await self._create_directories_async(base_path, structure.directories)

        # 并行写入文件
        file_tasks = []
        for file_path, content in rendered_files.items():
            full_path = base_path / file_path
            task = self._write_file_async(full_path, content)
            file_tasks.append(task)

        # 等待所有文件写入完成
        results = await asyncio.gather(*file_tasks, return_exceptions=True)

        # 处理结果
        created_files = []
        errors = []

        for i, result in enumerate(results):
            if isinstance(result, Exception):
                errors.append(f"写入文件失败: {result}")
            else:
                created_files.append(list(rendered_files.keys())[i])

        return GenerationResult(
            success=len(errors) == 0,
            created_files=created_files,
            errors=errors
        )

    async def _create_directories_async(self, base_path: Path, directories: List[str]):
        """异步创建目录"""
        for directory in directories:
            dir_path = base_path / directory
            dir_path.mkdir(parents=True, exist_ok=True)

    async def _write_file_async(self, file_path: Path, content: str):
        """异步写入文件"""
        import aiofiles

        # 确保父目录存在
        file_path.parent.mkdir(parents=True, exist_ok=True)

        async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
            await f.write(content)
```

## 测试设计

### 单元测试结构
```python
import pytest
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
import tempfile
import shutil

class TestInitializationController:
    """初始化控制器测试类"""

    @pytest.fixture
    def temp_dir(self):
        """临时目录fixture"""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)

    @pytest.fixture
    def mock_config(self):
        """模拟配置fixture"""
        return ProjectConfig(
            name="test-project",
            description="测试项目",
            tech_stack=["python"],
            team_size=1,
            team_experience="intermediate"
        )

    @pytest.fixture
    def controller(self):
        """控制器实例fixture"""
        return InitializationController()

    async def test_initialize_project_success(self, controller, mock_config, temp_dir):
        """测试成功初始化项目"""
        # Arrange
        project_name = "test-project"
        template = "basic"

        # Act
        result = await controller.initialize_project(
            name=project_name,
            template=template,
            config=mock_config.dict(),
            output_dir=str(temp_dir)
        )

        # Assert
        assert result.success is True
        assert result.project_path is not None
        assert len(result.created_files) > 0
        assert len(result.created_directories) > 0

        # 验证文件结构
        project_path = temp_dir / project_name
        assert project_path.exists()
        assert (project_path / ".spec").exists()
        assert (project_path / ".spec" / "project.md").exists()
        assert (project_path / "features").exists()

    async def test_initialize_project_already_exists(self, controller, mock_config, temp_dir):
        """测试项目已存在的情况"""
        # Arrange
        project_name = "existing-project"
        project_path = temp_dir / project_name
        project_path.mkdir()

        # Act
        result = await controller.initialize_project(
            name=project_name,
            template="basic",
            config=mock_config.dict(),
            output_dir=str(temp_dir),
            force=False
        )

        # Assert
        assert result.success is False
        assert "已存在" in result.error

    async def test_initialize_project_with_force(self, controller, mock_config, temp_dir):
        """测试强制覆盖已存在项目"""
        # Arrange
        project_name = "existing-project"
        project_path = temp_dir / project_name
        project_path.mkdir()
        (project_path / "old_file.txt").write_text("old content")

        # Act
        result = await controller.initialize_project(
            name=project_name,
            template="basic",
            config=mock_config.dict(),
            output_dir=str(temp_dir),
            force=True
        )

        # Assert
        assert result.success is True
        assert (project_path / ".spec").exists()

class TestTemplateEngine:
    """模板引擎测试类"""

    @pytest.fixture
    def template_engine(self, tmp_path):
        """模板引擎fixture"""
        template_dir = tmp_path / "templates"
        template_dir.mkdir()

        # 创建测试模板
        (template_dir / "test.md.j2").write_text(
            "# {{ project.name }}\n{{ project.description }}"
        )

        return TemplateEngine([str(template_dir)])

    def test_render_template(self, template_engine):
        """测试模板渲染"""
        # Arrange
        variables = {
            'project': {
                'name': 'Test Project',
                'description': 'A test project'
            }
        }

        # Act
        result = template_engine.render_template("test.md.j2", variables)

        # Assert
        assert "# Test Project" in result
        assert "A test project" in result

    def test_template_not_found(self, template_engine):
        """测试模板不存在的情况"""
        # Act & Assert
        with pytest.raises(TemplateNotFoundError):
            template_engine.render_template("nonexistent.j2", {})

class TestConfigurationCollector:
    """配置收集器测试类"""

    @pytest.fixture
    def collector(self):
        return ConfigurationCollector()

    def test_collect_from_args(self, collector):
        """测试从参数收集配置"""
        # Arrange
        args = {
            'name': 'test-project',
            'description': 'Test description',
            'tech_stack': ['python', 'fastapi'],
            'team_size': 3
        }

        # Act
        config = collector.collect_from_args(args)

        # Assert
        assert config.name == 'test-project'
        assert config.description == 'Test description'
        assert 'python' in config.tech_stack
        assert config.team_size == 3

    def test_validate_config_success(self, collector):
        """测试配置验证成功"""
        # Arrange
        config = ProjectConfig(
            name="valid-project",
            description="Valid description",
            tech_stack=["python"]
        )

        # Act
        result = collector.validate_config(config)

        # Assert
        assert result.is_valid is True
        assert len(result.errors) == 0

    def test_validate_config_invalid_name(self, collector):
        """测试无效项目名称"""
        # Arrange
        config = ProjectConfig(
            name="123-invalid",  # 不能以数字开头
            description="Valid description",
            tech_stack=["python"]
        )

        # Act & Assert
        with pytest.raises(ValidationError):
            collector.validate_config(config)
```

## 部署和分发设计

### 包结构设计
```python
# setup.py
from setuptools import setup, find_packages

setup(
    name="mcp-spec-tool",
    version="1.0.0",
    description="MCP规约驱动开发工具",
    long_description=open("README.md", encoding="utf-8").read(),
    long_description_content_type="text/markdown",
    author="个人开发者",
    author_email="<EMAIL>",
    url="https://github.com/username/mcp-spec-tool",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    include_package_data=True,
    package_data={
        "mcp_spec": [
            "templates/**/*",
            "templates/**/*.j2",
            "templates/**/*.json"
        ]
    },
    install_requires=[
        "click>=8.0.0",
        "jinja2>=3.0.0",
        "pydantic>=2.0.0",
        "rich>=13.0.0",
        "aiofiles>=23.0.0"
    ],
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "pytest-cov>=4.0.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0"
        ],
        "mcp": [
            "mcp>=0.1.0"
        ]
    },
    entry_points={
        "console_scripts": [
            "mcp-spec=mcp_spec.cli:main"
        ]
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12"
    ],
    python_requires=">=3.8"
)
```

### 配置管理设计
```python
from pathlib import Path
from typing import Optional, Dict, Any
import json
import os

class ConfigManager:
    """配置管理器"""

    DEFAULT_CONFIG_PATHS = [
        Path.home() / ".mcp-spec" / "config.json",
        Path.cwd() / ".mcp-spec" / "config.json",
        Path.cwd() / "mcp-spec.json"
    ]

    def __init__(self, config_path: Optional[Path] = None):
        self.config_path = config_path
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        config_paths = [self.config_path] if self.config_path else self.DEFAULT_CONFIG_PATHS

        for path in config_paths:
            if path and path.exists():
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        return json.load(f)
                except (json.JSONDecodeError, IOError) as e:
                    print(f"警告: 加载配置文件 {path} 失败: {e}")

        return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "templates": {
                "default_template": "basic",
                "template_dirs": [
                    str(Path(__file__).parent / "templates")
                ]
            },
            "ui": {
                "language": "zh-CN",
                "show_progress": True,
                "color_output": True
            },
            "mcp": {
                "enabled": True,
                "server_name": "spec-generator",
                "auto_start": True
            },
            "generation": {
                "backup_existing": True,
                "create_git_repo": False,
                "add_examples": True
            }
        }

    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        value = self.config

        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default

        return value

    def save(self, config_path: Optional[Path] = None) -> None:
        """保存配置文件"""
        save_path = config_path or self.config_path or self.DEFAULT_CONFIG_PATHS[0]
        save_path.parent.mkdir(parents=True, exist_ok=True)

        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=2)
```

---

**文档版本**: v1.0
**创建日期**: 2025-01-29
**负责人**: 个人开发者
**状态**: 📋 待开始
