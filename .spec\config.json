{"project": {"name": "MCP规约驱动开发工具", "description": "一个基于MCP（Model Context Protocol）的轻量级规约驱动开发工具，专为个人开发者和小团队设计，通过AI辅助将自然语言需求转化为结构化的开发规约", "tech_stack": ["Python 3.12+", "Click CLI框架", "MCP Python SDK", "Jinja2模板引擎"], "created_at": "2025-01-29", "version": "1.0.0"}, "mcp": {"server_name": "spec-generator", "tools": ["generate_spec", "list_tasks", "update_task_status", "sync_documents", "watch_documents"], "auto_start": true, "port": 8000}, "ai": {"provider": "mcp", "fallback_provider": "claude", "model": "claude-3-sonnet", "max_tokens": 4000, "temperature": 0.7}, "templates": {"requirements": "templates/requirements.md.j2", "design": "templates/design.md.j2", "tasks": "templates/tasks.md.j2", "project": "templates/project.md.j2", "architecture": "templates/architecture.md.j2", "conventions": "templates/conventions.md.j2"}, "preferences": {"language": "zh-CN", "output_format": "markdown", "auto_save": true, "mcp_integration": true, "interactive_mode": true}, "document_sync": {"auto_watch": true, "auto_update": false, "backup_before_update": true, "update_delay_seconds": 2, "excluded_files": [".spec/temp/*", "*.tmp", "*.log"], "watch_patterns": [".spec/*.md", "features/*/*.md"]}, "cli": {"default_template": "basic", "quiet_mode": false, "force_overwrite": false, "output_directory": ".", "show_progress": true}, "development": {"debug_mode": false, "log_level": "INFO", "log_file": ".spec/logs/mcp-spec.log", "enable_profiling": false}, "validation": {"strict_mode": false, "validate_on_save": true, "check_dependencies": true, "warn_on_conflicts": true}}