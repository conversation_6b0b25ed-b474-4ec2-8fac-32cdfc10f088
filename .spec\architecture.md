# MCP规约驱动开发工具 - 技术架构规约

## 架构概述

### 架构原则
- **轻量级优先**：本地运行，无需复杂基础设施
- **AI驱动**：通过MCP协议集成AI开发工具
- **本地优先**：保护代码隐私，敏感数据不传输到外部
- **标准化协议**：基于MCP标准，确保与多种AI工具兼容
- **快速迭代**：简单架构，支持快速开发和部署

### 整体架构

#### 系统分层
```
┌─────────────────────────────────────────────────────────────┐
│                    用户交互层                                │
├─────────────────────────────────────────────────────────────┤
│  CLI工具      │  AI开发工具  │  VS Code插件  │  文档界面    │
├─────────────────────────────────────────────────────────────┤
│                    MCP协议层                                │
├─────────────────────────────────────────────────────────────┤
│  MCP服务器    │  工具定义    │  消息处理     │  上下文管理  │
├─────────────────────────────────────────────────────────────┤
│                    核心业务层                                │
├─────────────────────────────────────────────────────────────┤
│  规约生成引擎 │  任务管理    │  文档同步     │  模板系统    │
├─────────────────────────────────────────────────────────────┤
│                    本地存储层                                │
├─────────────────────────────────────────────────────────────┤
│  Markdown文档 │  配置文件    │  模板文件     │  状态数据    │
└─────────────────────────────────────────────────────────────┘
```

#### 核心组件架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  AI开发工具     │    │   MCP服务器     │    │   CLI工具       │
│                 │    │                 │    │                 │
│ - Augment       │◄──►│ - 规约生成      │◄──►│ - 命令解析      │
│ - Cursor        │    │ - 模板管理      │    │ - 文件管理      │
│ - VS Code       │    │ - 上下文管理    │    │ - 状态跟踪      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   AI模型推理    │    │   规约模板      │    │   本地文件      │
│                 │    │                 │    │                 │
│ - Claude/GPT    │    │ - requirements  │    │ - .spec/        │
│ - 本地模型      │    │ - design        │    │ - features/     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 技术栈选择

### 核心技术栈

#### 开发语言和框架
- **开发语言**：Python 3.12+（跨平台，丰富的AI库支持）
- **CLI框架**：Click（简单易用的命令行框架）
- **MCP实现**：基于官方MCP Python SDK
- **AI集成**：通过MCP协议连接AI开发工具（Augment、Cursor等）

#### 依赖库
```python
# requirements.txt
click>=8.0.0
mcp>=0.1.0
openai>=1.0.0
anthropic>=0.3.0
pydantic>=2.0.0
jinja2>=3.0.0
gitpython>=3.1.0
rich>=13.0.0  # 美化命令行输出
watchdog>=3.0.0  # 文件监控
```

#### 数据存储
- **文档存储**：本地Markdown文件
- **配置存储**：JSON配置文件
- **状态存储**：本地文件系统
- **模板存储**：Jinja2模板文件

### AI集成架构

#### 支持的AI开发工具
- **Augment Code**：通过MCP服务器提供规约生成能力
- **Cursor**：集成到Cursor的AI工作流中
- **VS Code + Continue**：作为MCP工具在VS Code中使用
- **其他支持MCP的工具**：可扩展支持

#### MCP服务器配置
```json
{
  "mcpServers": {
    "spec-generator": {
      "command": "python",
      "args": ["-m", "mcp_spec.server"],
      "env": {
        "SPEC_CONFIG_PATH": "./.spec/config.json"
      }
    }
  }
}
```

#### MCP工具定义
- **generate_spec**：生成功能规约文档
- **list_tasks**：列出当前项目的任务
- **update_task_status**：更新任务状态
- **sync_documents**：同步更新相关规约文档
- **watch_documents**：启动文档监控

## 文件结构设计

### 项目文件结构
```
mcp-spec-tool/
├── src/
│   ├── mcp_spec/
│   │   ├── __init__.py
│   │   ├── cli.py           # 命令行入口
│   │   ├── core/
│   │   │   ├── generator.py # 规约生成核心
│   │   │   ├── parser.py    # 需求解析
│   │   │   ├── tracker.py   # 任务跟踪
│   │   │   └── watcher.py   # 文档监控
│   │   ├── mcp/
│   │   │   ├── server.py    # MCP服务器
│   │   │   └── handlers.py  # MCP处理器
│   │   ├── templates/       # 规约模板
│   │   └── utils/
│   └── tests/
├── templates/               # 项目模板
├── docs/
├── setup.py
└── README.md
```

### 用户项目文件结构
```
user-project/
├── .spec/
│   ├── project.md          # 项目宪法
│   ├── architecture.md     # 技术架构
│   ├── conventions.md      # 开发约定
│   └── config.json         # 工具配置
├── features/
│   ├── user-auth/
│   │   ├── requirements.md # 需求规约
│   │   ├── design.md       # 设计规约
│   │   └── tasks.md        # 任务分解
│   └── ...
└── .mcp/
    └── server-config.json  # MCP服务器配置
```

## 数据架构

### 配置文件结构
```json
{
  "project": {
    "name": "my-project",
    "description": "项目描述",
    "tech_stack": ["React", "Node.js", "MongoDB"],
    "created_at": "2025-01-29"
  },
  "mcp": {
    "server_name": "spec-generator",
    "tools": ["generate_spec", "list_tasks", "update_task_status"],
    "auto_start": true
  },
  "ai": {
    "provider": "mcp",
    "fallback_provider": "claude",
    "model": "claude-3-sonnet",
    "max_tokens": 4000
  },
  "document_sync": {
    "auto_watch": true,
    "auto_update": false,
    "backup_before_update": true,
    "update_delay_seconds": 2
  }
}
```

### 文档依赖关系
```python
# 文档依赖关系映射
DOCUMENT_DEPENDENCIES = {
    ".spec/project.md": [".spec/architecture.md", ".spec/conventions.md"],
    ".spec/architecture.md": ["features/*/design.md"],
    ".spec/conventions.md": ["features/*/design.md", "features/*/tasks.md"],
    "features/*/requirements.md": ["features/*/design.md", "features/*/tasks.md"],
    "features/*/design.md": ["features/*/tasks.md"]
}
```

### 数据流设计

#### 规约生成流程
```
用户输入 → MCP工具调用 → 意图解析 → 上下文加载 → AI模型调用 → 结果验证 → 文档生成 → 本地存储
```

#### 文档同步流程
```
文档变更 → 文件监控 → 变更检测 → 依赖分析 → 影响评估 → 增量更新 → 用户确认 → 同步完成
```

#### MCP集成流程
```
AI工具请求 → MCP服务器 → 工具路由 → 业务逻辑 → 结果返回 → AI工具展示
```

## 安全和隐私设计

### 本地优先原则
- **数据存储**：所有数据存储在本地文件系统
- **AI调用**：通过现有AI开发工具，不直接调用外部API
- **敏感信息**：项目代码和规约文档不传输到外部服务
- **配置管理**：配置文件本地存储，支持版本控制

### 数据安全
- **文件权限**：使用操作系统文件权限保护
- **备份机制**：文档更新前自动备份
- **版本控制**：支持Git集成，变更可追溯
- **配置加密**：敏感配置项支持本地加密存储

## 性能设计

### 性能目标
- **响应时间**：规约生成 < 30秒
- **资源占用**：内存使用 < 100MB，CPU使用 < 10%
- **启动时间**：CLI工具启动 < 2秒，MCP服务器启动 < 5秒
- **文件处理**：支持处理大型项目（1000+文件）

### 性能优化策略
- **本地缓存**：模板和配置文件缓存
- **增量处理**：只处理变更的文档部分
- **异步处理**：文档监控和同步使用异步机制
- **防抖机制**：避免频繁的文档更新操作

### 扩展性设计
- **模块化架构**：核心功能模块化，支持插件扩展
- **模板系统**：支持自定义项目模板和规约模板
- **配置驱动**：通过配置文件支持不同的使用场景
- **工具集成**：标准MCP协议，支持多种AI开发工具

## 部署和分发

### 安装方式
- **Python包管理**：`pip install mcp-spec-tool`
- **源码安装**：`git clone && pip install -e .`
- **预编译包**：支持Windows、macOS、Linux

### 配置管理
- **自动配置**：首次运行自动生成配置文件
- **配置验证**：启动时验证配置文件完整性
- **配置迁移**：支持配置文件版本升级
- **环境变量**：支持通过环境变量覆盖配置

### MCP服务器部署
```bash
# 启动MCP服务器
python -m mcp_spec.server

# 在AI开发工具中配置
{
  "mcpServers": {
    "spec-generator": {
      "command": "python",
      "args": ["-m", "mcp_spec.server"]
    }
  }
}
```

---

**文档版本**：v2.0
**创建日期**：2025-01-29
**更新日期**：2025-01-29
**负责人**：个人开发者
**架构类型**：轻量级本地工具
