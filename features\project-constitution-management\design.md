# 项目宪法管理 - 设计规约

## 设计概述

### 设计目标
基于需求规约，设计一个高可用、易扩展的项目宪法管理系统，支持文档的创建、编辑、版本控制和权限管理。

### 设计原则
- **用户体验优先**：提供直观、流畅的用户界面
- **数据一致性**：确保文档数据的完整性和一致性
- **高可用性**：系统设计支持高并发和故障恢复
- **安全性**：严格的权限控制和数据保护
- **可扩展性**：支持未来功能扩展和性能优化

## 系统架构设计

### 整体架构
```mermaid
graph TB
    subgraph "前端层"
        A[Web管理界面]
        B[富文本编辑器]
        C[版本对比组件]
    end
    
    subgraph "API网关层"
        D[API Gateway]
        E[认证中间件]
        F[权限中间件]
    end
    
    subgraph "业务服务层"
        G[宪法管理服务]
        H[文档编辑服务]
        I[版本控制服务]
        J[权限管理服务]
    end
    
    subgraph "数据存储层"
        K[PostgreSQL]
        L[MongoDB]
        M[Redis缓存]
        N[文件存储]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    F --> G
    F --> H
    F --> I
    F --> J
    G --> K
    H --> L
    I --> K
    J --> K
    G --> M
    H --> N
```

### 服务架构设计

#### 宪法管理服务 (Constitution Service)
**职责**：
- 项目宪法的创建和初始化
- 模板管理和应用
- 宪法元数据管理
- 业务规则验证

**接口设计**：
```typescript
interface ConstitutionService {
  createConstitution(projectId: string, template: string, info: ProjectInfo): Promise<Constitution>;
  getConstitution(constitutionId: string): Promise<Constitution>;
  updateConstitutionInfo(constitutionId: string, info: Partial<ProjectInfo>): Promise<Constitution>;
  deleteConstitution(constitutionId: string): Promise<void>;
  listTemplates(): Promise<Template[]>;
}
```

#### 文档编辑服务 (Document Service)
**职责**：
- 文档内容的CRUD操作
- 实时编辑支持
- 文档格式验证
- 自动保存机制

**接口设计**：
```typescript
interface DocumentService {
  getDocument(constitutionId: string, documentType: DocumentType): Promise<Document>;
  updateDocument(constitutionId: string, documentType: DocumentType, content: string): Promise<Document>;
  validateDocument(content: string, documentType: DocumentType): Promise<ValidationResult>;
  autoSave(constitutionId: string, documentType: DocumentType, content: string): Promise<void>;
}
```

#### 版本控制服务 (Version Service)
**职责**：
- 版本创建和管理
- 版本对比和差异计算
- 版本回滚操作
- 版本历史查询

**接口设计**：
```typescript
interface VersionService {
  createVersion(documentId: string, content: string, note: string): Promise<Version>;
  getVersionHistory(documentId: string): Promise<Version[]>;
  compareVersions(documentId: string, version1: string, version2: string): Promise<VersionDiff>;
  rollbackToVersion(documentId: string, versionId: string): Promise<Document>;
}
```

## 数据库设计

### 数据模型设计

#### 核心实体关系图
```mermaid
erDiagram
    PROJECT ||--|| PROJECT_CONSTITUTION : has
    PROJECT_CONSTITUTION ||--o{ CONSTITUTION_DOCUMENT : contains
    CONSTITUTION_DOCUMENT ||--o{ DOCUMENT_VERSION : has
    PROJECT_CONSTITUTION }o--|| CONSTITUTION_TEMPLATE : uses
    PROJECT_CONSTITUTION }o--o{ USER : manages
    
    PROJECT {
        uuid id PK
        string name
        string description
        timestamp created_at
    }
    
    PROJECT_CONSTITUTION {
        uuid id PK
        uuid project_id FK
        string name
        string description
        uuid template_id FK
        string status
        uuid created_by FK
        timestamp created_at
        timestamp updated_at
    }
    
    CONSTITUTION_DOCUMENT {
        uuid id PK
        uuid constitution_id FK
        string document_type
        string title
        text content
        string current_version
        uuid created_by FK
        timestamp created_at
        timestamp updated_at
    }
    
    DOCUMENT_VERSION {
        uuid id PK
        uuid document_id FK
        string version_number
        text content
        string version_note
        uuid created_by FK
        timestamp created_at
    }
```

#### 详细表结构

##### 项目宪法表
```sql
CREATE TABLE project_constitutions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL REFERENCES projects(id),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    template_id UUID REFERENCES constitution_templates(id),
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'archived', 'deleted')),
    metadata JSONB DEFAULT '{}',
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_active_constitution_per_project 
        UNIQUE (project_id) WHERE status = 'active'
);

CREATE INDEX idx_project_constitutions_project_id ON project_constitutions(project_id);
CREATE INDEX idx_project_constitutions_status ON project_constitutions(status);
CREATE INDEX idx_project_constitutions_created_by ON project_constitutions(created_by);
```

##### 宪法文档表
```sql
CREATE TABLE constitution_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    constitution_id UUID NOT NULL REFERENCES project_constitutions(id) ON DELETE CASCADE,
    document_type VARCHAR(50) NOT NULL CHECK (document_type IN ('project', 'architecture', 'conventions')),
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    current_version VARCHAR(20) NOT NULL DEFAULT '1.0.0',
    word_count INTEGER DEFAULT 0,
    last_edited_by UUID REFERENCES users(id),
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_document_type_per_constitution 
        UNIQUE (constitution_id, document_type)
);

CREATE INDEX idx_constitution_documents_constitution_id ON constitution_documents(constitution_id);
CREATE INDEX idx_constitution_documents_type ON constitution_documents(document_type);
CREATE INDEX idx_constitution_documents_updated_at ON constitution_documents(updated_at);
```

##### 文档版本表
```sql
CREATE TABLE document_versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID NOT NULL REFERENCES constitution_documents(id) ON DELETE CASCADE,
    version_number VARCHAR(20) NOT NULL,
    content TEXT NOT NULL,
    content_hash VARCHAR(64) NOT NULL, -- SHA-256 hash for deduplication
    version_note TEXT,
    word_count INTEGER DEFAULT 0,
    changes_summary JSONB DEFAULT '{}', -- {"added": 100, "deleted": 50, "modified": 25}
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_version_per_document UNIQUE (document_id, version_number)
);

CREATE INDEX idx_document_versions_document_id ON document_versions(document_id);
CREATE INDEX idx_document_versions_created_at ON document_versions(created_at);
CREATE INDEX idx_document_versions_content_hash ON document_versions(content_hash);
```

### 数据访问层设计

#### Repository接口
```typescript
// 宪法管理Repository
interface ConstitutionRepository {
  create(constitution: CreateConstitutionDto): Promise<Constitution>;
  findById(id: string): Promise<Constitution | null>;
  findByProjectId(projectId: string): Promise<Constitution | null>;
  update(id: string, updates: Partial<Constitution>): Promise<Constitution>;
  delete(id: string): Promise<void>;
  findByTemplate(templateId: string): Promise<Constitution[]>;
}

// 文档Repository
interface DocumentRepository {
  create(document: CreateDocumentDto): Promise<Document>;
  findById(id: string): Promise<Document | null>;
  findByConstitutionAndType(constitutionId: string, type: DocumentType): Promise<Document | null>;
  update(id: string, content: string, version: string): Promise<Document>;
  updateWordCount(id: string, wordCount: number): Promise<void>;
  findByConstitution(constitutionId: string): Promise<Document[]>;
}

// 版本Repository
interface VersionRepository {
  create(version: CreateVersionDto): Promise<Version>;
  findById(id: string): Promise<Version | null>;
  findByDocument(documentId: string, limit?: number): Promise<Version[]>;
  findByDocumentAndVersion(documentId: string, versionNumber: string): Promise<Version | null>;
  deleteOldVersions(documentId: string, keepCount: number): Promise<void>;
}
```

## 前端设计

### 组件架构设计

#### 页面组件结构
```
ConstitutionManagement/
├── ConstitutionList/           # 宪法列表页面
│   ├── ConstitutionCard.tsx    # 宪法卡片组件
│   ├── CreateButton.tsx        # 创建按钮组件
│   └── SearchFilter.tsx        # 搜索筛选组件
├── ConstitutionEditor/         # 宪法编辑页面
│   ├── DocumentTabs.tsx        # 文档标签页组件
│   ├── MarkdownEditor.tsx      # Markdown编辑器
│   ├── PreviewPanel.tsx        # 预览面板
│   └── VersionHistory.tsx      # 版本历史组件
├── ConstitutionCreator/        # 宪法创建向导
│   ├── TemplateSelector.tsx    # 模板选择器
│   ├── ProjectInfoForm.tsx     # 项目信息表单
│   └── PreviewStep.tsx         # 预览步骤
└── shared/                     # 共享组件
    ├── PermissionGuard.tsx     # 权限守卫组件
    ├── AutoSaveIndicator.tsx   # 自动保存指示器
    └── VersionCompare.tsx      # 版本对比组件
```

#### 核心组件设计

##### Markdown编辑器组件
```typescript
interface MarkdownEditorProps {
  value: string;
  onChange: (value: string) => void;
  onSave: () => void;
  readOnly?: boolean;
  autoSave?: boolean;
  placeholder?: string;
}

export const MarkdownEditor: React.FC<MarkdownEditorProps> = ({
  value,
  onChange,
  onSave,
  readOnly = false,
  autoSave = true,
  placeholder
}) => {
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  
  // 自动保存逻辑
  useEffect(() => {
    if (!autoSave || readOnly) return;
    
    const timer = setTimeout(() => {
      onSave();
      setLastSaved(new Date());
    }, 2000);
    
    return () => clearTimeout(timer);
  }, [value, autoSave, readOnly, onSave]);
  
  return (
    <div className="markdown-editor">
      <div className="editor-toolbar">
        <ToolbarButton 
          icon="bold" 
          onClick={() => insertText('**', '**')}
          tooltip="粗体"
        />
        <ToolbarButton 
          icon="italic" 
          onClick={() => insertText('*', '*')}
          tooltip="斜体"
        />
        <ToolbarButton 
          icon="code" 
          onClick={() => insertText('`', '`')}
          tooltip="代码"
        />
        <div className="toolbar-divider" />
        <ToolbarButton 
          icon={isPreviewMode ? 'edit' : 'eye'}
          onClick={() => setIsPreviewMode(!isPreviewMode)}
          tooltip={isPreviewMode ? '编辑模式' : '预览模式'}
        />
        {lastSaved && (
          <span className="auto-save-indicator">
            最后保存: {formatTime(lastSaved)}
          </span>
        )}
      </div>
      
      <div className="editor-content">
        {isPreviewMode ? (
          <MarkdownPreview content={value} />
        ) : (
          <CodeMirror
            value={value}
            onChange={onChange}
            extensions={[markdown(), oneDark]}
            readOnly={readOnly}
            placeholder={placeholder}
          />
        )}
      </div>
    </div>
  );
};
```

##### 版本历史组件
```typescript
interface VersionHistoryProps {
  documentId: string;
  currentVersion: string;
  onVersionSelect: (version: Version) => void;
  onVersionRestore: (version: Version) => void;
}

export const VersionHistory: React.FC<VersionHistoryProps> = ({
  documentId,
  currentVersion,
  onVersionSelect,
  onVersionRestore
}) => {
  const { data: versions, isLoading } = useGetVersionHistoryQuery(documentId);
  const [selectedVersions, setSelectedVersions] = useState<[string, string] | null>(null);
  
  const handleCompareVersions = () => {
    if (selectedVersions) {
      // 打开版本对比模态框
      openVersionCompareModal(selectedVersions[0], selectedVersions[1]);
    }
  };
  
  return (
    <div className="version-history">
      <div className="version-history-header">
        <h3>版本历史</h3>
        <Button 
          onClick={handleCompareVersions}
          disabled={!selectedVersions || selectedVersions.length !== 2}
        >
          对比版本
        </Button>
      </div>
      
      <div className="version-list">
        {versions?.map((version) => (
          <div 
            key={version.id}
            className={`version-item ${version.versionNumber === currentVersion ? 'current' : ''}`}
          >
            <Checkbox
              checked={selectedVersions?.includes(version.id)}
              onChange={(checked) => handleVersionSelect(version.id, checked)}
            />
            <div className="version-info">
              <div className="version-header">
                <span className="version-number">v{version.versionNumber}</span>
                <span className="version-date">{formatDate(version.createdAt)}</span>
              </div>
              <div className="version-note">{version.versionNote}</div>
              <div className="version-meta">
                <span>by {version.createdBy.name}</span>
                <span>{version.changesSummary.added}+ {version.changesSummary.deleted}-</span>
              </div>
            </div>
            <div className="version-actions">
              <Button size="small" onClick={() => onVersionSelect(version)}>
                查看
              </Button>
              {version.versionNumber !== currentVersion && (
                <Button 
                  size="small" 
                  type="primary" 
                  onClick={() => onVersionRestore(version)}
                >
                  恢复
                </Button>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
```

### 状态管理设计

#### Redux Store结构
```typescript
interface RootState {
  constitution: {
    current: Constitution | null;
    documents: Record<DocumentType, Document>;
    versions: Record<string, Version[]>;
    templates: Template[];
    loading: boolean;
    error: string | null;
  };
  editor: {
    activeDocument: DocumentType | null;
    content: Record<DocumentType, string>;
    isDirty: Record<DocumentType, boolean>;
    autoSaveEnabled: boolean;
    lastSaved: Record<DocumentType, Date | null>;
  };
  permissions: {
    canEdit: boolean;
    canManage: boolean;
    canDelete: boolean;
  };
}
```

#### RTK Query API定义
```typescript
export const constitutionApi = createApi({
  reducerPath: 'constitutionApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/v1/constitution',
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as RootState).auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['Constitution', 'Document', 'Version'],
  endpoints: (builder) => ({
    getConstitution: builder.query<Constitution, string>({
      query: (id) => `/${id}`,
      providesTags: ['Constitution'],
    }),
    updateDocument: builder.mutation<Document, UpdateDocumentRequest>({
      query: ({ constitutionId, documentType, content, versionNote }) => ({
        url: `/${constitutionId}/documents/${documentType}`,
        method: 'PUT',
        body: { content, versionNote },
      }),
      invalidatesTags: ['Document', 'Version'],
    }),
    getVersionHistory: builder.query<Version[], string>({
      query: (documentId) => `/documents/${documentId}/versions`,
      providesTags: ['Version'],
    }),
  }),
});
```

## 后端设计

### 服务实现设计

#### 宪法管理服务实现
```typescript
@Injectable()
export class ConstitutionService {
  constructor(
    private readonly constitutionRepository: ConstitutionRepository,
    private readonly documentService: DocumentService,
    private readonly templateService: TemplateService,
    private readonly permissionService: PermissionService,
  ) {}
  
  async createConstitution(
    projectId: string,
    templateId: string,
    projectInfo: ProjectInfo,
    userId: string,
  ): Promise<Constitution> {
    // 验证权限
    await this.permissionService.checkProjectPermission(projectId, userId, 'manage');
    
    // 检查是否已存在活跃的宪法
    const existing = await this.constitutionRepository.findByProjectId(projectId);
    if (existing && existing.status === 'active') {
      throw new ConflictException('Project already has an active constitution');
    }
    
    // 获取模板
    const template = await this.templateService.getTemplate(templateId);
    if (!template) {
      throw new NotFoundException('Template not found');
    }
    
    // 创建宪法
    const constitution = await this.constitutionRepository.create({
      projectId,
      name: projectInfo.name,
      description: projectInfo.description,
      templateId,
      createdBy: userId,
    });
    
    // 基于模板创建文档
    await this.createDocumentsFromTemplate(constitution.id, template, projectInfo, userId);
    
    return constitution;
  }
  
  private async createDocumentsFromTemplate(
    constitutionId: string,
    template: Template,
    projectInfo: ProjectInfo,
    userId: string,
  ): Promise<void> {
    const documentTypes: DocumentType[] = ['project', 'architecture', 'conventions'];
    
    for (const documentType of documentTypes) {
      const templateContent = template.documents[documentType];
      const processedContent = this.processTemplate(templateContent, projectInfo);
      
      await this.documentService.createDocument({
        constitutionId,
        documentType,
        title: this.getDocumentTitle(documentType),
        content: processedContent,
        createdBy: userId,
      });
    }
  }
  
  private processTemplate(template: string, projectInfo: ProjectInfo): string {
    return template
      .replace(/\{\{projectName\}\}/g, projectInfo.name)
      .replace(/\{\{projectDescription\}\}/g, projectInfo.description)
      .replace(/\{\{techStack\}\}/g, projectInfo.techStack.join(', '))
      .replace(/\{\{teamSize\}\}/g, projectInfo.teamSize.toString());
  }
}
```

#### 文档编辑服务实现
```typescript
@Injectable()
export class DocumentService {
  constructor(
    private readonly documentRepository: DocumentRepository,
    private readonly versionService: VersionService,
    private readonly validationService: ValidationService,
    private readonly cacheService: CacheService,
  ) {}
  
  async updateDocument(
    constitutionId: string,
    documentType: DocumentType,
    content: string,
    versionNote: string,
    userId: string,
  ): Promise<Document> {
    // 获取当前文档
    const document = await this.documentRepository.findByConstitutionAndType(
      constitutionId,
      documentType,
    );
    
    if (!document) {
      throw new NotFoundException('Document not found');
    }
    
    // 验证内容
    const validationResult = await this.validationService.validateDocument(
      content,
      documentType,
    );
    
    if (!validationResult.isValid) {
      throw new BadRequestException('Document validation failed', validationResult.errors);
    }
    
    // 检查内容是否有变更
    if (document.content === content) {
      return document; // 无变更，直接返回
    }
    
    // 创建新版本
    const newVersion = await this.versionService.createVersion({
      documentId: document.id,
      content,
      versionNote,
      createdBy: userId,
    });
    
    // 更新文档
    const updatedDocument = await this.documentRepository.update(
      document.id,
      content,
      newVersion.versionNumber,
    );
    
    // 更新缓存
    await this.cacheService.invalidate(`document:${document.id}`);
    
    // 发送更新事件
    this.eventEmitter.emit('document.updated', {
      documentId: document.id,
      constitutionId,
      documentType,
      version: newVersion.versionNumber,
      userId,
    });
    
    return updatedDocument;
  }
  
  async autoSave(
    constitutionId: string,
    documentType: DocumentType,
    content: string,
    userId: string,
  ): Promise<void> {
    const cacheKey = `autosave:${constitutionId}:${documentType}:${userId}`;
    
    // 保存到缓存，设置过期时间
    await this.cacheService.set(cacheKey, {
      content,
      timestamp: new Date(),
    }, 3600); // 1小时过期
  }
  
  async getAutoSavedContent(
    constitutionId: string,
    documentType: DocumentType,
    userId: string,
  ): Promise<string | null> {
    const cacheKey = `autosave:${constitutionId}:${documentType}:${userId}`;
    const cached = await this.cacheService.get(cacheKey);
    
    return cached?.content || null;
  }
}
```

### API接口实现

#### 控制器设计
```typescript
@Controller('constitution')
@UseGuards(JwtAuthGuard)
@ApiTags('Constitution Management')
export class ConstitutionController {
  constructor(private readonly constitutionService: ConstitutionService) {}
  
  @Post('projects/:projectId/constitution')
  @ApiOperation({ summary: '创建项目宪法' })
  @ApiResponse({ status: 201, type: ConstitutionDto })
  async createConstitution(
    @Param('projectId') projectId: string,
    @Body() createDto: CreateConstitutionDto,
    @CurrentUser() user: User,
  ): Promise<ConstitutionDto> {
    const constitution = await this.constitutionService.createConstitution(
      projectId,
      createDto.templateId,
      createDto.projectInfo,
      user.id,
    );
    
    return ConstitutionDto.fromEntity(constitution);
  }
  
  @Get(':id')
  @ApiOperation({ summary: '获取项目宪法' })
  @ApiResponse({ status: 200, type: ConstitutionDto })
  async getConstitution(
    @Param('id') id: string,
    @CurrentUser() user: User,
  ): Promise<ConstitutionDto> {
    const constitution = await this.constitutionService.getConstitution(id);
    
    // 检查权限
    await this.permissionService.checkConstitutionPermission(id, user.id, 'read');
    
    return ConstitutionDto.fromEntity(constitution);
  }
  
  @Put(':id/documents/:documentType')
  @ApiOperation({ summary: '更新文档内容' })
  @ApiResponse({ status: 200, type: DocumentDto })
  async updateDocument(
    @Param('id') constitutionId: string,
    @Param('documentType') documentType: DocumentType,
    @Body() updateDto: UpdateDocumentDto,
    @CurrentUser() user: User,
  ): Promise<DocumentDto> {
    // 检查编辑权限
    await this.permissionService.checkConstitutionPermission(constitutionId, user.id, 'edit');
    
    const document = await this.documentService.updateDocument(
      constitutionId,
      documentType,
      updateDto.content,
      updateDto.versionNote,
      user.id,
    );
    
    return DocumentDto.fromEntity(document);
  }
}
```

---

## 性能优化设计

### 缓存策略
- **文档内容缓存**：使用Redis缓存频繁访问的文档内容
- **版本历史缓存**：缓存最近的版本历史，减少数据库查询
- **模板缓存**：缓存常用模板，提高创建速度
- **权限缓存**：缓存用户权限信息，减少权限检查开销

### 数据库优化
- **读写分离**：读操作使用只读副本，写操作使用主库
- **索引优化**：为常用查询字段创建合适的索引
- **分页查询**：大数据量查询使用游标分页
- **连接池**：合理配置数据库连接池大小

### 前端优化
- **代码分割**：按路由和功能模块进行代码分割
- **懒加载**：非关键组件使用懒加载
- **虚拟滚动**：长列表使用虚拟滚动技术
- **防抖节流**：用户输入使用防抖，滚动事件使用节流

---

**文档版本**：v1.0
**创建日期**：2025-01-29
**设计负责人**：架构师
**开发负责人**：后端团队Lead
**预估工作量**：15人天
**技术栈**：React + TypeScript + Spring Boot + PostgreSQL
